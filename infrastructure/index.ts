import * as aws from '@pulumi/aws';
import {
  addGlobalDynamoPermission,
  addGlobalQueuePermission,
  alarmEmail,
  setGlobalEnvVar,
  stageName,
} from './config';
import { createLambdaFunction } from './lambda';
import { subscribeEmailToAlarmTopic } from './queueMonitoring';
import { createAdminResources } from './resources/admin';
import { createAdMobResources } from './resources/admob';
import { createApiRoutes } from './resources/api';
import { createDatabaseTables } from './resources/database';
import { createDevResources } from './resources/dev';
import { createEmailResources } from './resources/email';
import { createFixtureResources } from './resources/fixtures';
import { createGameworldResources } from './resources/gameworld';
import { createIapResources } from './resources/iap';
import { createLeagueResources } from './resources/league';
import { createManagerResources } from './resources/manager';
import { createPlayerResources } from './resources/player';
import { createScoutingResources } from './resources/scouting';
import { createTeamResources } from './resources/team';
import { createTrainingResources } from './resources/training';
import { createTransferResources } from './resources/transfers';
import { createSesResources, outputSesVerificationRecords } from './ses';

// Create SES resources for email sending
const { domainIdentity, domainDkim, domainMailFrom, emailIdentity } = createSesResources();

// Output SES verification records for DNS configuration
if (stageName !== 'dev') {
  outputSesVerificationRecords(domainIdentity!, domainDkim!, domainMailFrom!);
}

// Create DynamoDB Tables
const { fixtureDetailTable, inboxTable } = createDatabaseTables();

// Create email processing resources
const { emailQueue, emailQueueUrl, errorAlarmTopic: emailErrorAlarmTopic } = createEmailResources();

// Set up global Lambda configuration
// Set the email queue URL as a global environment variable
// This will be automatically included in all Lambda functions
setGlobalEnvVar('EMAIL_QUEUE_URL', emailQueueUrl);

// Set the inbox table name as a global environment variable
setGlobalEnvVar('INBOX_TABLE_NAME', inboxTable.name);

// Add global permission for all Lambda functions to send messages to the email queue
// This will be automatically included in all Lambda function roles
addGlobalQueuePermission('email', emailQueue, 'send');

// Add global permission for all Lambda functions to read and write to the inbox table
addGlobalDynamoPermission('inbox', inboxTable, 'both');

// Create HTTP Lambda Functions
// Create the ping Lambda with the SES role
const [pingLambda] = createLambdaFunction('ping', '../dist/ping/ping', 'index.handler');

const {
  endOfSeasonQueue,
  teamQueue,
  teamDLQ,
  unattachedPlayersQueue,
  unattachedPlayersDLQ,
  errorAlarmTopic: gameworldErrorAlarmTopic,
  countAvailableTeamsLambda,
  fixtureGenerationQueue,
} = createGameworldResources({ fixtureDetailTable });

const {
  playerQueue,
  playerDLQ,
  getTeamLambda,
  updateTeamOrderLambda,
  updateTeamNameLambda,
  upgradeTrainingLambda,
  errorAlarmTopic: teamErrorAlarmTopic,
  getTransactionsLambda,
} = createTeamResources({
  teamQueue,
  teamDLQ,
  fixtureGenerationQueue,
});

const {
  getTransferListPlayersLambda,
  getMyBidTransferListPlayersLambda,
  useMagicSpongeLambda,
  useRedCardAppealLambda,
  getPlayerStatsLambda,
  errorAlarmTopic: playerErrorAlarmTopic,
} = createPlayerResources({
  playerQueue,
  playerDLQ,
  unattachedPlayersQueue,
  unattachedPlayersDLQ,
});

const {
  getManagerLambda,
  postConfirmationLambda,
  updateManagerNameLambda,
  updateNotificationPreferencesLambda,
  getInboxMessagesLambda,
  getRewardsLambda,
  unsubscribeLambda,
} = createManagerResources({ playerQueue });

const { getLeaguesLambda, getLeagueLambda } = createLeagueResources({});

const {
  requestScoutingLambda,
  requestSuperScoutingLambda,
  getScoutedPlayersLambda,
  errorAlarmTopic,
} = createScoutingResources({
  getManagerLambda,
});

const {
  getFixturesLambda,
  getTeamFixturesLambda,
  getFixtureLambda,
  fixtureQueue,
  getCommentaryLambda,
  errorAlarmTopic: fixtureErrorAlarmTopic,
} = createFixtureResources({
  getTeamLambda,
  fixtureDetailTable,
});

const {
  submitTransferOfferLambda,
  submitBidLambda,
  myActiveTransfersLambda,
  acceptTransferRequestLambda,
  cancelTransferRequestLambda,
  releasePlayerLambda,
  errorAlarmTopic: transferErrorAlarmTopic,
  toggleTransferListStatusLambda,
} = createTransferResources({
  emailQueue,
  unattachedPlayersQueue,
});

const {
  getTrainingSlotsLambda,
  assignTrainingSlotLambda,
  unlockTrainingSlotLambda,
  errorAlarmTopic: trainingErrorAlarmTopic,
} = createTrainingResources({});

const localOnlyRoutes = createDevResources({
  getFixturesLambda,
  fixtureQueue,
  endOfSeasonQueue,
});

// First create the role
const xrayRole = new aws.iam.Role(`${stageName}-xray-role`, {
  assumeRolePolicy: JSON.stringify({
    Version: '2012-10-17',
    Statement: [
      {
        Action: 'sts:AssumeRole',
        Principal: {
          Service: ['lambda.amazonaws.com', 'apigateway.amazonaws.com'],
        },
        Effect: 'Allow',
      },
    ],
  }),
});

// Then attach the policy to the role
new aws.iam.RolePolicy(`${stageName}-xray-policy`, {
  role: xrayRole.id, // Use the role's id here, not the name
  policy: JSON.stringify({
    Version: '2012-10-17',
    Statement: [
      {
        Effect: 'Allow',
        Action: [
          'xray:PutTraceSegments',
          'xray:PutTelemetryRecords',
          'xray:GetSamplingRules',
          'xray:GetSamplingTargets',
          'xray:GetSamplingStatisticSummaries',
        ],
        Resource: ['*'],
      },
    ],
  }),
});

const { ssvLambda } = createAdMobResources();

const { feedbackLambda, versionCheckLambda } = createAdminResources();

const {
  iapLambda,
  getIAPRewardsLambda,
  errorAlarmTopic: iapErrorAlarmTopic,
} = createIapResources();

const { api, cognitoUserPoolId, cognitoUserPoolClientId } = createApiRoutes({
  pingLambda,
  postConfirmationLambda,
  getLeaguesLambda,
  getLeagueLambda,
  getManagerLambda,
  getRewardsLambda,
  updateManagerNameLambda,
  updateNotificationPreferencesLambda,
  getInboxMessagesLambda,
  getTeamLambda,
  updateTeamOrderLambda,
  updateTeamNameLambda,
  upgradeTrainingLambda,
  getTransferListPlayersLambda,
  getMyBidTransferListPlayersLambda,
  getFixturesLambda,
  getTeamFixturesLambda,
  getFixtureLambda,
  getCommentaryLambda,
  useMagicSpongeLambda,
  useRedCardAppealLambda,
  getPlayerStatsLambda,
  requestScoutingLambda,
  requestSuperScoutingLambda,
  getScoutedPlayersLambda,
  submitTransferOfferLambda,
  submitBidLambda,
  myActiveTransfersLambda,
  acceptTransferRequestLambda,
  cancelTransferRequestLambda,
  releasePlayerLambda,
  getTrainingSlotsLambda,
  assignTrainingSlotLambda,
  unlockTrainingSlotLambda,
  ssvLambda,
  getTransactionsLambda,
  countAvailableTeamsLambda,
  feedbackLambda,
  versionCheckLambda,
  iapLambda,
  getIAPRewardsLambda,
  toggleTransferListStatusLambda,
  unsubscribeLambda,
  localOnlyRoutes,
});

// Export the URL of the API
export const url = api.stage.invokeUrl;

// Export the Cognito IDs as stack outputs
export const userPoolId = cognitoUserPoolId;
export const userPoolClientId = cognitoUserPoolClientId;

// Export SES resources
export const sesEmailIdentity = emailIdentity?.email;
export const sesDomainIdentity = domainIdentity?.domain;

// Subscribe email to error alarm topics if email is provided
if (alarmEmail) {
  // Subscribe to all error alarm topics
  subscribeEmailToAlarmTopic('scouting-error-logs', errorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('team-error-logs', teamErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('player-error-logs', playerErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('gameworld-error-logs', gameworldErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('fixture-error-logs', fixtureErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('email-error-logs', emailErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('transfer-error-logs', transferErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('training-error-logs', trainingErrorAlarmTopic, alarmEmail);
  subscribeEmailToAlarmTopic('iap-error-logs', iapErrorAlarmTopic, alarmEmail);
}
