const esbuild = require('esbuild');
const glob = require('glob');
const path = require('path');
const { fileURLToPath } = require('url');
const fs = require('fs');

const esbuildPluginTsc = require('esbuild-plugin-tsc');

//const __filename = fileURLToPath(import.meta.url);
//const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Generate entry points with custom output paths
const entryPoints = {};
glob.sync('./src/functions/**/!(*.test).ts').forEach((file) => {
  const relativePath = path.relative('./src/functions', file);
  const name = relativePath.replace(/\.ts$/, '');
  entryPoints[`${name}/index`] = file;
});

const externalPackages = [
  'mariadb',
  'mariadb/callback',
  'better-sqlite3',
  'libsql',
  'mysql2',
  'tedious',
  'sqlite3',
  'mysql',
  'oracledb',
  'pg-query-stream',
  'aws-sdk',
];

esbuild
  .build({
    entryPoints,
    bundle: true,
    platform: 'node',
    target: 'node20',
    outdir: 'dist',
    format: 'cjs',
    sourcemap: true,
    minify: process.env.STAGE !== 'dev',
    // Tree shaking is enabled by default in esbuild
    treeShaking: true,
    // Only exclude native modules that Lambda already provides
    external: externalPackages,
    absWorkingDir: rootDir,
    tsconfig: './tsconfig.json',
    plugins: [
      esbuildPluginTsc({
        forceEsm: true,
      }),
    ],
  })
  .then(() => {
    console.log('Build complete');

    // Create templates directory in dist
    const templatesDir = path.join(rootDir, 'dist', 'templates');
    if (!fs.existsSync(templatesDir)) {
      fs.mkdirSync(templatesDir, { recursive: true });
    }

    // Copy email template to dist
    const sourceTemplatePath = path.join(
      rootDir,
      'src',
      'services',
      'email',
      'templates',
      'basic.mjml'
    );
    const destTemplatePath = path.join(templatesDir, 'basic.mjml');

    try {
      if (fs.existsSync(sourceTemplatePath)) {
        fs.copyFileSync(sourceTemplatePath, destTemplatePath);
        console.log('Email template copied to dist/templates');
      } else {
        console.warn('Email template not found at:', sourceTemplatePath);
      }
    } catch (error) {
      console.error('Error copying email template:', error);
    }

    // Copy all MJML templates to dist/templates
    const sourceTemplatesDir = path.join(rootDir, 'src', 'services', 'email', 'templates');
    const mjmlFiles = fs.readdirSync(sourceTemplatesDir).filter((file) => file.endsWith('.mjml'));
    mjmlFiles.forEach((file) => {
      const sourceFile = path.join(sourceTemplatesDir, file);
      const destFile = path.join(templatesDir, file);
      fs.copyFileSync(sourceFile, destFile);
    });
    console.log('All MJML templates copied to dist/templates');
  });
