import { Player } from '@/entities/Player.ts';
import { Team } from '@/entities/Team.js';
import { Repositories } from '@/middleware/database/types.ts';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { TransactionType } from '@/storage-interface/teams/index.js';
import { logger } from '@/utils/logger.js';

const notificationManager = NotificationManager.getInstance();

async function notifyManagersOfTransfer(
  player: Player,
  winningTeam: Team,
  sellingTeam: Team | null,
  receivedFee: number,
  repositories: Repositories
) {
  const playerName = player.firstName + ' ' + player.surname;

  if (winningTeam.manager) {
    notificationManager.assignManagerPreferences(winningTeam.manager, repositories);
    await notificationManager.auctionWon(winningTeam, playerName);
  }
  if (sellingTeam && sellingTeam.manager) {
    notificationManager.assignManagerPreferences(sellingTeam.manager, repositories);
    await notificationManager.playerSold(winningTeam, playerName, receivedFee);
  }
}

/**
 * Check if the transfer fee is a new record sale/purchase for the managers involved
 * @param transferFee
 * @param winningTeam
 * @param sellingTeam
 * @param repositories
 */
async function updateManagerStats(
  transferFee: number,
  winningTeam: Team,
  sellingTeam: Team | null,
  repositories: Repositories
) {
  if (sellingTeam && sellingTeam.manager) {
    if (transferFee > sellingTeam.manager.highestTransferReceived) {
      await repositories.managerRepository.updateManager(sellingTeam.manager.managerId, {
        highestTransferReceived: transferFee,
      });
    }
  }
  if (winningTeam.manager && transferFee > winningTeam.manager.highestTransferPaid) {
    await repositories.managerRepository.updateManager(winningTeam.manager.managerId, {
      highestTransferPaid: transferFee,
    });
  }
}

export async function completePlayerTransfer(
  player: Player,
  winningTeam: Team,
  sellingTeam: Team | null,
  transferFee: number,
  repositories: Repositories
) {
  // subtract the winning bid amount from the winning team's balance
  await repositories.teamRepository.updateTeamBalance(
    winningTeam.teamId,
    winningTeam.gameworldId,
    -transferFee,
    TransactionType.TRANSFER
  );
  logger.debug('Updated winning team balance', {
    teamId: winningTeam.teamId,
    gameworldId: winningTeam.gameworldId,
    amount: -transferFee,
  });

  // add 80% of the winning bid amount to the auction player's team balance
  if (sellingTeam) {
    await repositories.teamRepository.updateTeamBalance(
      sellingTeam.teamId,
      sellingTeam.gameworldId,
      transferFee * 0.8,
      TransactionType.TRANSFER
    );
    logger.debug('Updated selling team balance', {
      teamId: sellingTeam.teamId,
      gameworldId: sellingTeam.gameworldId,
      amount: transferFee * 0.8,
    });
  }

  // add the player to the winning team's squad
  await repositories.playerRepository.assignPlayerToTeam(
    winningTeam.gameworldId,
    player.playerId,
    winningTeam.teamId
  );
  logger.debug('Assigned player to team', {
    playerId: player.playerId,
    teamId: winningTeam.teamId,
    gameworldId: winningTeam.gameworldId,
  });

  // Notify managers
  await notifyManagersOfTransfer(player, winningTeam, sellingTeam, transferFee, repositories);
  // Check if we need to update manager stats
  await updateManagerStats(transferFee, winningTeam, sellingTeam, repositories);
}
