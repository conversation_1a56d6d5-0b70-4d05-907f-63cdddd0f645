import { logger } from '@/utils/logger.js';
import { CognitoJwtVerifier } from 'aws-jwt-verify';
import { APIGatewayAuthorizerResult, APIGatewayTokenAuthorizerEvent } from 'aws-lambda';

const USER_POOL_ID = process.env.COGNITO_USER_POOL_ID!;
const CLIENT_ID = process.env.COGNITO_CLIENT_ID!;

// Create a verifier that expects valid access tokens
const verifier = CognitoJwtVerifier.create({
  userPoolId: USER_POOL_ID,
  tokenUse: 'access',
  clientId: CLIENT_ID,
});

// Helper to generate the IAM policy
const generatePolicy = (
  userId: string,
  effect: 'Allow' | 'Deny',
  resource: string,
  context: Record<string, any> = {}
): APIGatewayAuthorizerResult => {
  return {
    principalId: userId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: effect,
          Resource: resource,
        },
      ],
    },
    context,
  };
};

export const handler = async (
  event: APIGatewayTokenAuthorizerEvent
): Promise<APIGatewayAuthorizerResult> => {
  try {
    // Remove 'Bearer ' from the token
    const token = event.authorizationToken.replace('Bearer ', '');

    // Verify the JWT token
    const decoded = await verifier.verify(token);

    // Generate an Allow policy for all methods on all paths
    const policy = generatePolicy(
      decoded.sub,
      'Allow',
      event.methodArn.split('/')[0] + '/*/*', // This allows access to all methods/paths
      {
        // Additional context that will be passed to your Lambda functions
        userId: decoded.sub,
        username: decoded.username,
        email: decoded.email,
        // Add any other claims you want to pass through
      }
    );

    return policy;
  } catch (error) {
    logger.error('Authorization failed:', { error });
    throw new Error('Unauthorized'); // API Gateway will convert this to a 401
  }
};
