import { Manager } from '@/entities/Manager.js';
import { Team } from '@/entities/Team.js';
import { TransactionType } from '@/storage-interface/teams/index.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import {
  mockManagerRepository,
  mockScoutingRequestRepository,
  mockTeamRepository,
} from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './requestScouting.js';

vi.mock('@/utils/getUser', () => ({
  getUser: vi.fn().mockReturnValue('test-user-id'),
}));

describe('Request Scouting Lambda', () => {
  const mockContext = {} as any;

  const mockManager: Manager = ManagerFactory.build({
    managerId: 'test-user-id',
    gameworldId: 'test-gameworld-id',
    scoutTokens: 3,
  });

  const mockTeam: Team = TeamsFactory.build({
    teamId: 'test-team-id',
    gameworldId: 'test-gameworld-id',
    balance: 10000,
  });

  beforeEach(() => {
    process.env.MANAGERS_TABLE_NAME = 'managers-table';
    process.env.TEAMS_TABLE_NAME = 'teams-table';
    process.env.SCOUTING_REQUESTS_TABLE_NAME = 'scouting-requests-table';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should successfully process a valid scouting request', async () => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date(1740178572294));

    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    // Add repository context that middleware would normally provide
    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
      team: { teamId: mockManager.team!.teamId },
    });
    mockManagerRepository.updateManager.mockResolvedValue(undefined);
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);
    mockTeamRepository.updateTeamBalance.mockResolvedValue(undefined);
    mockScoutingRequestRepository.createScoutingRequest.mockResolvedValue(undefined);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body).toEqual({
      message: 'Scouting request successful',
      remainingTokens: 2,
      newBalance: 5000,
    });

    // Verify repository operations
    expect(mockManagerRepository.getManagerById).toHaveBeenCalledWith('test-user-id');
    expect(mockManagerRepository.updateManager).toHaveBeenCalledWith('test-user-id', {
      scoutTokens: 2,
    });

    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith(
      'test-gameworld-id',
      mockManager.team!.teamId,
      false
    );
    expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
      'test-team-id',
      'test-gameworld-id',
      -5000,
      TransactionType.SCOUTING
    );

    expect(mockScoutingRequestRepository.createScoutingRequest).toHaveBeenCalledWith(
      'test-gameworld-id',
      mockManager.team!.teamId,
      'test-user-id',
      'player',
      'test-player-id',
      1740185772294
    );

    vi.useRealTimers();
  });

  it('should return an error when no manager found', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'invalid-type',
        id: 'test-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue(null);

    const response = await handler(event, mockContext);
    // The function doesn't validate request body format, it returns 404 when manager is not found
    expect(response.statusCode).toBe(404);
    const body = JSON.parse(response.body);
    expect(body.error).toBe('Manager not found');
  });

  it('should reject when manager has no scout tokens', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    // Add repository context with manager having no scout tokens
    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
      scoutTokens: 0,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('No scouting tokens remaining');
    expect(mockManagerRepository.updateManager).not.toHaveBeenCalled();
  });

  it('should reject when team has insufficient funds', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    // Add repository context with team having insufficient funds
    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
    });
    mockTeamRepository.getTeam.mockResolvedValue({ ...mockTeam, balance: 4000 });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toContain('Insufficient funds');
    expect(mockManagerRepository.updateManager).not.toHaveBeenCalled();
    expect(mockTeamRepository.updateTeamBalance).not.toHaveBeenCalled();
  });

  it('should return 404 when team is not found', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    // Add repository context with team not found
    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
    });
    mockTeamRepository.getTeam.mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Team not found');
    expect(mockManagerRepository.updateManager).not.toHaveBeenCalled();
    expect(mockTeamRepository.updateTeamBalance).not.toHaveBeenCalled();
  });

  it.each(['player', 'team', 'league'])('should accept valid type: %s', async (type) => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type,
        id: 'test-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
    });
    mockManagerRepository.updateManager.mockResolvedValue(undefined);
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);
    mockTeamRepository.updateTeamBalance.mockResolvedValue(undefined);
    mockScoutingRequestRepository.createScoutingRequest.mockResolvedValue(undefined);

    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
  });
});
