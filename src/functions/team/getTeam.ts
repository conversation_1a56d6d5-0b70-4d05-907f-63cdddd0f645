import { Player } from '@/entities/Player.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { PotentialAttributes } from '@/model/player.js';
import { GetTeamResponse, PlayerResponse } from '@/model/team.js';
import { extractCurrentAttributes, extractPotentialAttributes } from '@/utils/attributeUtils.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

interface PathParameters {
  teamId: string;
  gameworldId: string;
}

interface QueryParameters {
  includePlayers?: boolean;
}

export type GetTeamEvent = HttpEvent<void, PathParameters, QueryParameters>;

function getTrainingPotential(player: Player) {
  let totalFraction = 0;
  let count = 0;

  const attrs = player.attributes;
  for (const key in attrs) {
    if (key.endsWith('Current')) {
      const current = (attrs as any)[key] as number;
      const potentialKey = key.replace('Current', 'Potential');
      const potential = (attrs as any)[potentialKey] as number;

      const maxRemaining = 40 - current;
      if (maxRemaining > 0) {
        const remaining = potential - current;
        totalFraction += remaining / maxRemaining;
        count++;
      }
    }
  }

  return count > 0 ? totalFraction / count : 0;
}

function getHighestPotentialAttributes(player: Player): string[] {
  const attrs = player.attributes;
  const potentialAttrs: Record<string, number> = {};

  for (const key in attrs) {
    if (key.endsWith('Current')) {
      const current = (attrs as any)[key] as number;
      const potentialKey = key.replace('Current', 'Potential');
      const potential = (attrs as any)[potentialKey] as number;

      const attrName = key.replace('Current', '');
      potentialAttrs[attrName] = potential - current;
    }
  }

  // Sort attributes by potential value in descending order
  const sortedAttrs = Object.entries(potentialAttrs).sort((a, b) => b[1] - a[1]);

  // Return the top 3 attributes
  return sortedAttrs.slice(0, 3).map(([attr]) => attr);
}

const main = async function (event: GetTeamEvent) {
  const { teamRepository, managerRepository } = event.context.repositories;

  let teamId: string;
  if (event.pathParameters && event.pathParameters.teamId) {
    teamId = event.pathParameters.teamId;
  } else {
    teamId = getUser(event);
  }

  const { team, nextFixture } = await teamRepository.getTeamAndNextMatch(
    event.pathParameters.gameworldId,
    teamId,
    event.queryStringParameters?.includePlayers || false
  );
  if (!team) {
    return buildResponse(404, JSON.stringify({ error: 'Team not found' }));
  }

  // Get the current user ID
  const userId = getUser(event);
  if (!userId) {
    return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
  }

  // Get the manager to find the team and gameworld information
  const manager = await managerRepository.getManagerById(userId, false);
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  const { manager: teamManager, ...teamWithoutManager } = team;
  const teamResponse: GetTeamResponse = { ...teamWithoutManager, players: [] };

  if (event.queryStringParameters?.includePlayers) {
    if (team.players) {
      const orderedPlayers: PlayerResponse[] = [];
      const restructuredPlayers: PlayerResponse[] = team.players.map((player) => {
        // Extract only the current attribute values using the helper function
        const currentAttributes = extractCurrentAttributes(player.attributes);
        let potentialAttributes: PotentialAttributes | undefined;
        if (manager.role === 'admin') {
          // admins get potential attributes too
          potentialAttributes = extractPotentialAttributes(player.attributes);
        }

        // Return player with current attributes only
        return {
          ...player,
          team: undefined,
          seed: undefined,
          lastMatchPlayed: Number(player.lastMatchPlayed),
          teamId: team.teamId,
          attributes: currentAttributes,
          potentialAttributes,
          trainingPotential: getTrainingPotential(player),
          topTrainingAreas: getHighestPotentialAttributes(player),
        } as PlayerResponse;
      });

      // return the players in the order they were selected
      if (team.selectionOrder) {
        // Create a map for O(1) lookup of players
        const playerMap = new Map(restructuredPlayers.map((player) => [player.playerId, player]));

        // First, add players in the selection order
        team.selectionOrder.forEach((playerId) => {
          const player = playerMap.get(playerId);
          if (player) {
            orderedPlayers.push(player);
            playerMap.delete(playerId);
          }
        });

        // any players not in the selection order will be added to the end
        orderedPlayers.push(...playerMap.values());
      } else {
        orderedPlayers.push(...restructuredPlayers);
      }

      // Replace the players collection with our ordered and transformed players
      teamResponse.players = orderedPlayers;
    }
  }
  if (nextFixture) {
    teamResponse.nextFixture = {
      fixtureId: nextFixture.fixtureId,
      date: nextFixture.date,
    };
  }

  return buildResponse(
    200,
    JSON.stringify(teamResponse, (_key, value) =>
      typeof value === 'bigint' ? Number(value) : value
    )
  );
};

export const handler = httpMiddify(main, {});
