import { Manager } from '@/entities/Manager.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { mockManagerRepository, resetAllRepositoryMocks } from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it } from 'vitest';
import { handler } from './unsubscribe.js';

// Import buildHtmlResponse from the correct path

describe('unsubscribe lambda', () => {
  let event: any;
  let context = {} as any;
  let manager: Manager;

  beforeEach(() => {
    manager = ManagerFactory.build({
      managerId: '1',
      notificationPreferences: {
        transfers: { email: true, push: true },
        training: { email: true, push: false },
      },
    });
    resetAllRepositoryMocks();
    mockManagerRepository.getManagerById.mockResolvedValue(manager);
    mockManagerRepository.updateManager.mockResolvedValue(undefined);
    event = createHttpEvent({
      queryStringParameters: { id: '1', category: 'transfers' },
      httpMethod: 'GET',
    });
  });

  it('should only update email for the specified category', async () => {
    const result = await handler(event, context);
    expect(mockManagerRepository.updateManager).toHaveBeenCalledWith(
      '1',
      expect.objectContaining({
        notificationPreferences: expect.objectContaining({
          transfers: expect.objectContaining({ email: false, push: true }),
          training: expect.objectContaining({ email: true, push: false }),
        }),
      })
    );
  });

  it('should not affect other categories', async () => {
    await handler(event, context);
    const update = mockManagerRepository.updateManager.mock.calls[0]![1];
    expect(update.notificationPreferences.training).toEqual({ email: true, push: false });
  });

  it('should create the category if it does not exist', async () => {
    manager.notificationPreferences = {};
    await handler(event, context);
    const update = mockManagerRepository.updateManager.mock.calls[0]![1];
    expect(update.notificationPreferences.transfers).toEqual({ email: false });
  });

  it('should return 200 HTML if manager not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);
    const response = await handler(event, context);
    expect(response.statusCode).toBe(200);
    expect(response.headers!['Content-Type']).toBe('text/html; charset=utf-8');
    expect(response.body).toContain('Manager not found.');
  });
});
