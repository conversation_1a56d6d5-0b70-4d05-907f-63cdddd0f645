import { Manager } from '@/entities/Manager.ts';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { NotificationCategory, NotificationChannel } from '@/model/manager.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface NotificationType {
  push: boolean;
  email: boolean;
}

interface RequestBody {
  transfers: NotificationType;
  preMatch: NotificationType;
  scoutingResults: NotificationType;
  postMatch: NotificationType;
  announcements: NotificationType;
  training: NotificationType;
  pushNotificationToken?: string;
}

export type UpdateNotificationPreferencesEvent = HttpEvent<RequestBody, void, void>;

const main = async function (event: UpdateNotificationPreferencesEvent) {
  const { managerRepository } = event.context.repositories;

  // Get the current user ID from the event
  const userId = getUser(event);

  // Check if the manager exists
  const manager = await managerRepository.getManagerById(userId);
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  // Create updates object with only the fields that were provided
  const updates: Partial<Manager> = {
    notificationPreferences: {
      [NotificationCategory.TRANSFERS]: {
        [NotificationChannel.PUSH]: event.body.transfers.push,
        [NotificationChannel.EMAIL]: event.body.transfers.email,
      },
      [NotificationCategory.PRE_MATCH]: {
        [NotificationChannel.PUSH]: event.body.preMatch.push,
        [NotificationChannel.EMAIL]: event.body.preMatch.email,
      },
      [NotificationCategory.SCOUTING_RESULTS]: {
        [NotificationChannel.PUSH]: event.body.scoutingResults.push,
        [NotificationChannel.EMAIL]: event.body.scoutingResults.email,
      },
      [NotificationCategory.POST_MATCH]: {
        [NotificationChannel.PUSH]: event.body.postMatch.push,
        [NotificationChannel.EMAIL]: event.body.postMatch.email,
      },
      [NotificationCategory.SYSTEM_ANNOUNCEMENTS]: {
        [NotificationChannel.PUSH]: event.body.announcements.push,
        [NotificationChannel.EMAIL]: event.body.announcements.email,
      },
      [NotificationCategory.TRAINING]: {
        [NotificationChannel.PUSH]: event.body.training.push,
        [NotificationChannel.EMAIL]: event.body.training.email,
      },
    },
    ...(event.body.pushNotificationToken !== undefined
      ? {
          pushToken:
            event.body.pushNotificationToken === null
              ? undefined
              : event.body.pushNotificationToken,
        }
      : {}),
  };
  try {
    // Update the manager
    await managerRepository.updateManager(userId, updates);

    return buildResponse(200, '');
  } catch (error) {
    logger.error('Error updating manager:', { error });
    return buildResponse(500, JSON.stringify({ error: 'Failed to update manager' }));
  }
};

export const handler = httpMiddify(main, {});
