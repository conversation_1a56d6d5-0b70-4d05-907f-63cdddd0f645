import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON>ey, Property, type Rel, Unique } from '@mikro-orm/core';
import { NotificationCategory, NotificationChannel } from '../model/manager.js';
import { Team } from './Team.js';

// this is mirrored in model/manager.ts because for some reason the build fails if its imported
type NotificationPreferences = {
  [key in NotificationCategory]?: {
    [channel in NotificationChannel]?: boolean;
  };
};

@Entity({ tableName: 'manager' })
@Unique({ name: 'manager_id_key', properties: ['managerId'] })
export class Manager {
  @PrimaryKey({ type: 'uuid' })
  managerId!: string;

  @Property({ type: 'bigint' })
  createdAt!: number;

  @Property({ type: 'bigint' })
  lastActive!: number;

  @Property({ length: 100, nullable: true })
  firstName?: string;

  @Property({ length: 100, nullable: true })
  lastName?: string;

  @Property({ type: 'string', nullable: true })
  email?: string;

  @OneToOne({ entity: () => Team, fieldName: 'team_id', nullable: true })
  team?: Rel<Team>;

  @Property({ type: 'uuid', nullable: true })
  gameworldId?: string;

  @Property({ type: 'integer' })
  scoutTokens: number = 2;

  @Property({ type: 'integer' })
  superScoutTokens: number = 0;

  @Property({ type: 'integer' })
  magicSponges: number = 1;

  @Property({ type: 'integer' })
  cardAppeals: number = 0;

  @Property({ type: 'integer' })
  trainingBoosts: number = 0;

  @Property({ type: 'json', nullable: true })
  notificationPreferences?: NotificationPreferences = {};

  @Property({ nullable: true, type: 'string' })
  pushToken?: string;

  @Property({ type: 'integer' })
  loginStreak: number = 0;

  @Property({ type: 'string', default: 'user' })
  role: string = 'user'; // Default role is 'user', can be changed to 'admin'

  @Property({ type: 'boolean', default: false })
  changedTeamName: boolean = false;

  // Manager stats
  @Property({ type: 'integer', default: 0 })
  wins: number = 0;

  @Property({ type: 'integer', default: 0 })
  defeats: number = 0;

  @Property({ type: 'integer', default: 0 })
  draws: number = 0;

  @Property({ type: 'integer', default: 0 })
  goalsScored: number = 0;

  @Property({ type: 'integer', default: 0 })
  goalsConceded: number = 0;

  @Property({ type: 'integer', default: 0 })
  highestTransferPaid: number = 0;

  @Property({ type: 'integer', default: 0 })
  highestTransferReceived: number = 0;

  @Property({ type: 'integer', default: 0 })
  trophies: number = 0;
}
